use anyhow::{Context, Result};
use dialoguer::Select;
use std::fs;
use std::path::{Path, PathBuf};
use tempfile::TempDir;
use walkdir::WalkDir;

/// Create a temporary directory for processing
pub fn create_temp_dir(prefix: &str) -> Result<TempDir> {
    tempfile::Builder::new()
        .prefix(prefix)
        .tempdir()
        .with_context(|| {
            format!(
                "Failed to create temporary directory with prefix: {}",
                prefix
            )
        })
}

/// Ensure a directory exists, creating it if necessary
pub fn ensure_dir_exists<P: AsRef<Path>>(path: P) -> Result<()> {
    let path = path.as_ref();
    if !path.exists() {
        fs::create_dir_all(path)
            .with_context(|| format!("Failed to create directory: {}", path.display()))?;
    }
    Ok(())
}

/// Get the filename without extension from a path
pub fn get_filename_without_extension<P: AsRef<Path>>(path: P) -> Option<String> {
    path.as_ref()
        .file_stem()
        .and_then(|stem| stem.to_str())
        .map(|s| s.to_string())
}

/// Generate a default output path based on input path
pub fn generate_output_path<P: AsRef<Path>>(input_path: P, suffix: &str) -> PathBuf {
    let input_path = input_path.as_ref();

    if let Some(parent) = input_path.parent() {
        if let Some(filename) = get_filename_without_extension(input_path) {
            return parent.join(format!("{}{}.mrpack", filename, suffix));
        }
    }

    // Fallback
    PathBuf::from(format!("output{}.mrpack", suffix))
}

/// Validate that a file exists and has the correct extension
pub fn validate_mrpack_file<P: AsRef<Path>>(path: P) -> Result<()> {
    let path = path.as_ref();

    if !path.exists() {
        return Err(anyhow::anyhow!("File does not exist: {}", path.display()));
    }

    if !path.is_file() {
        return Err(anyhow::anyhow!("Path is not a file: {}", path.display()));
    }

    if let Some(extension) = path.extension() {
        if extension != "mrpack" {
            return Err(anyhow::anyhow!(
                "File does not have .mrpack extension: {}",
                path.display()
            ));
        }
    } else {
        return Err(anyhow::anyhow!(
            "File has no extension (expected .mrpack): {}",
            path.display()
        ));
    }

    Ok(())
}

/// Create a sample client-mods.txt file if it doesn't exist
pub fn create_sample_client_mods_list<P: AsRef<Path>>(path: P) -> Result<()> {
    let path = path.as_ref();

    if path.exists() {
        return Ok(()); // Don't overwrite existing file
    }

    let sample_content = r#"# Client-side mods list for Gitrinth
# Add mod names (partial matching supported) one per line
# Lines starting with # are comments and will be ignored
# Empty lines are also ignored

# Performance mods (client-only)
sodium
immediatelyfast
particle # matches all mods with particle in the jar name


# Client-side utility mods
allow-portal-gui


# Client-side cosmetic mods
iris

"#;

    fs::write(path, sample_content).with_context(|| {
        format!(
            "Failed to create sample client mods list: {}",
            path.display()
        )
    })?;

    println!("Created sample client-mods.txt file at: {}", path.display());
    println!("Please edit this file to match your modpack's client-side mods.");

    Ok(())
}

/// Print a formatted header
pub fn print_header(title: &str) {
    let border = "=".repeat(title.len() + 4);
    println!("\n{}", border);
    println!("  {}  ", title);
    println!("{}\n", border);
}

/// Print a formatted step
pub fn print_step(step: u32, description: &str) {
    println!("[{}] {}", step, description);
}

/// Auto-detect .mrpack file in current directory if only one exists, or prompt user to choose
pub fn auto_detect_mrpack_file() -> Result<Option<PathBuf>> {
    let current_dir = std::env::current_dir().with_context(|| "Failed to get current directory")?;

    let mut mrpack_files = Vec::new();

    // Look for .mrpack files in current directory
    for entry in fs::read_dir(&current_dir)
        .with_context(|| format!("Failed to read directory: {}", current_dir.display()))?
    {
        let entry = entry.with_context(|| "Failed to read directory entry")?;
        let path = entry.path();

        if path.is_file() {
            if let Some(extension) = path.extension() {
                if extension == "mrpack" {
                    mrpack_files.push(path);
                }
            }
        }
    }

    match mrpack_files.len() {
        0 => Ok(None),
        1 => {
            let file = &mrpack_files[0];
            println!("🔍 Auto-detected .mrpack file: {}", file.display());
            Ok(Some(file.clone()))
        }
        _ => {
            println!("🔍 Multiple .mrpack files found in directory:");

            // Create display names for the selection menu
            let display_names: Vec<String> = mrpack_files
                .iter()
                .map(|path| {
                    path.file_name()
                        .and_then(|name| name.to_str())
                        .unwrap_or("Unknown file")
                        .to_string()
                })
                .collect();

            // Show interactive selection menu
            let selection = Select::new()
                .with_prompt("Please choose which .mrpack file to use")
                .items(&display_names)
                .default(0)
                .interact()
                .with_context(|| "Failed to get user selection")?;

            let selected_file = &mrpack_files[selection];
            println!("✅ Selected: {}", selected_file.display());
            Ok(Some(selected_file.clone()))
        }
    }
}

/// Generate default server pack output name from input file
pub fn generate_server_pack_name<P: AsRef<Path>>(input_path: P, suffix: &str) -> PathBuf {
    let input_path = input_path.as_ref();

    if let Some(parent) = input_path.parent() {
        if let Some(filename) = get_filename_without_extension(input_path) {
            return parent.join(format!("{}{}.mrpack", filename, suffix));
        }
    }

    // Fallback
    PathBuf::from(format!("output{}.mrpack", suffix))
}

/// Copy all contents from source directory to destination directory
pub fn copy_dir_contents<P: AsRef<Path>>(src: P, dst: P) -> Result<()> {
    let src = src.as_ref();
    let dst = dst.as_ref();

    for entry in WalkDir::new(src) {
        let entry = entry
            .with_context(|| format!("Failed to read directory entry in: {}", src.display()))?;
        let path = entry.path();

        // Skip the root directory itself
        if path == src {
            continue;
        }

        // Calculate relative path from source
        let relative_path = path
            .strip_prefix(src)
            .with_context(|| format!("Failed to strip prefix from path: {}", path.display()))?;
        let dest_path = dst.join(relative_path);

        if path.is_dir() {
            // Create directory
            fs::create_dir_all(&dest_path)
                .with_context(|| format!("Failed to create directory: {}", dest_path.display()))?;
        } else {
            // Copy file
            if let Some(parent) = dest_path.parent() {
                fs::create_dir_all(parent).with_context(|| {
                    format!("Failed to create parent directory: {}", parent.display())
                })?;
            }

            fs::copy(path, &dest_path).with_context(|| {
                format!(
                    "Failed to copy file from {} to {}",
                    path.display(),
                    dest_path.display()
                )
            })?;
        }
    }

    Ok(())
}
