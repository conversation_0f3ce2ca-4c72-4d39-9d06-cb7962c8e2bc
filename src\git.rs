use anyhow::{Context, Result};
use dialoguer::Input;
use git2::{IndexAddOption, Repository, Signature};
use std::path::Path;

/// Git operations for the application
pub struct GitManager {
    repo: Repository,
}

impl GitManager {
    /// Try to open an existing git repository in the given directory
    pub fn open<P: AsRef<Path>>(path: P) -> Result<Self> {
        let repo = Repository::open(path.as_ref()).with_context(|| {
            format!(
                "Failed to open git repository at: {}",
                path.as_ref().display()
            )
        })?;

        Ok(GitManager { repo })
    }

    /// Initialize a new git repository in the given directory
    pub fn init<P: AsRef<Path>>(path: P) -> Result<Self> {
        let repo = Repository::init(path.as_ref()).with_context(|| {
            format!(
                "Failed to initialize git repository at: {}",
                path.as_ref().display()
            )
        })?;

        Ok(GitManager { repo })
    }

    /// Try to open existing repo, or initialize a new one if it doesn't exist
    pub fn open_or_init<P: AsRef<Path>>(path: P) -> Result<Self> {
        match Self::open(path.as_ref()) {
            Ok(manager) => Ok(manager),
            Err(_) => {
                println!("No git repository found. Initializing new repository...");
                Self::init(path)
            }
        }
    }

    /// Check if there's an existing git repository in the directory
    pub fn exists<P: AsRef<Path>>(path: P) -> bool {
        Repository::open(path.as_ref()).is_ok()
    }

    /// Add all files in the repository to the index
    pub fn add_all(&self) -> Result<()> {
        let mut index = self
            .repo
            .index()
            .with_context(|| "Failed to get repository index")?;

        index
            .add_all(["*"].iter(), IndexAddOption::DEFAULT, None)
            .with_context(|| "Failed to add files to index")?;

        index.write().with_context(|| "Failed to write index")?;

        Ok(())
    }

    /// Commit changes with a user-provided message
    pub fn commit_with_prompt(&self) -> Result<()> {
        // Check if there are any changes to commit
        let statuses = self
            .repo
            .statuses(None)
            .with_context(|| "Failed to get repository status")?;

        if statuses.is_empty() {
            println!("No changes to commit.");
            return Ok(());
        }

        // Show status
        println!("\nRepository status:");
        for entry in statuses.iter() {
            let status = entry.status();
            let path = entry.path().unwrap_or("<unknown>");

            if status.is_index_new() {
                println!("  A  {}", path);
            } else if status.is_index_modified() {
                println!("  M  {}", path);
            } else if status.is_index_deleted() {
                println!("  D  {}", path);
            } else if status.is_wt_new() {
                println!("  ?? {}", path);
            } else if status.is_wt_modified() {
                println!("  M  {}", path);
            } else if status.is_wt_deleted() {
                println!("  D  {}", path);
            }
        }

        // Get commit message from user
        let commit_message: String = Input::new()
            .with_prompt("Enter commit message")
            .with_initial_text("Add modpack files")
            .interact_text()
            .with_context(|| "Failed to get commit message from user")?;

        if commit_message.trim().is_empty() {
            println!("Empty commit message. Skipping commit.");
            return Ok(());
        }

        self.commit(&commit_message)
    }

    /// Commit changes with the given message
    pub fn commit(&self, message: &str) -> Result<()> {
        let signature = self
            .get_signature()
            .with_context(|| "Failed to create git signature")?;

        let mut index = self
            .repo
            .index()
            .with_context(|| "Failed to get repository index")?;
        let tree_id = index.write_tree().with_context(|| "Failed to write tree")?;
        let tree = self
            .repo
            .find_tree(tree_id)
            .with_context(|| "Failed to find tree")?;

        // Get parent commit if it exists
        let parent_commit = match self.repo.head() {
            Ok(head) => {
                let oid = head.target().context("Failed to get HEAD target")?;
                Some(
                    self.repo
                        .find_commit(oid)
                        .context("Failed to find parent commit")?,
                )
            }
            Err(_) => None, // First commit
        };

        let parents: Vec<&git2::Commit> = match &parent_commit {
            Some(commit) => vec![commit],
            None => vec![],
        };

        self.repo
            .commit(
                Some("HEAD"),
                &signature,
                &signature,
                message,
                &tree,
                &parents,
            )
            .with_context(|| "Failed to create commit")?;

        println!("Successfully committed changes: {}", message);
        Ok(())
    }

    /// Get or create a git signature
    fn get_signature(&self) -> Result<Signature> {
        // Try to get signature from git config
        let config = self.repo.config().ok();

        if let Some(config) = config {
            if let (Ok(name), Ok(email)) = (
                config.get_string("user.name"),
                config.get_string("user.email"),
            ) {
                return Signature::now(&name, &email)
                    .with_context(|| "Failed to create signature from git config");
            }
        }

        // Fallback to a default signature
        Signature::now("Gitrinth", "gitrinth@localhost")
            .with_context(|| "Failed to create default signature")
    }

    /// Get the current repository path
    pub fn path(&self) -> &Path {
        self.repo.path()
    }
}
