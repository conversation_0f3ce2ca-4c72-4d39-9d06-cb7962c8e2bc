# Build script for Gitrinth release
Write-Host "Building Gitrinth for release..." -ForegroundColor Green

# Clean previous builds
Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
cargo clean

# Build in release mode
Write-Host "Building in release mode..." -ForegroundColor Yellow
cargo build --release

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build successful!" -ForegroundColor Green
    Write-Host "Executable location: target\release\gitrinth.exe" -ForegroundColor Cyan
    
    # Show file size
    $exe = Get-Item "target\release\gitrinth.exe"
    $sizeKB = [math]::Round($exe.Length / 1KB, 2)
    Write-Host "File size: $sizeKB KB" -ForegroundColor Cyan
    
    # Test the executable
    Write-Host "`nTesting executable..." -ForegroundColor Yellow
    & "target\release\gitrinth.exe" --version
    
    Write-Host "`nBuild complete! You can now use:" -ForegroundColor Green
    Write-Host "  .\target\release\gitrinth.exe --help" -ForegroundColor White
} else {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}
