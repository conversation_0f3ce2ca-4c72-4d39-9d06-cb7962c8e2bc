pub mod config;
pub mod filter;
pub mod git;
pub mod mrpack;
pub mod utils;

use anyhow::{Context, Result};
use std::path::{Path, PathBuf};

pub use crate::config::GitrinthConfig;
use crate::filter::{C<PERSON>ModsList, ServerPackFilter};
use crate::git::GitManager;
use crate::mrpack::MrpackExtraction;
use crate::utils::{
    ReplacementMode, auto_detect_mrpack_file, copy_dir_contents_with_mode,
    create_sample_client_mods_list, generate_server_pack_name, print_header, print_step,
    validate_mrpack_file,
};

/// Main application logic for extracting .mrpack files
pub fn extract_mrpack<P: AsRef<Path>>(
    input: Option<P>,
    output: Option<P>,
    no_git: bool,
) -> Result<()> {
    print_header("Gitrinth - Extract Mode");

    // Determine input file (auto-detect if not provided)
    let input_path = match input {
        Some(path) => path.as_ref().to_path_buf(),
        None => {
            print_step(1, "Auto-detecting .mrpack file");
            match auto_detect_mrpack_file()? {
                Some(path) => path,
                None => {
                    return Err(anyhow::anyhow!(
                        "No .mrpack file specified and could not auto-detect one. Please specify with --input"
                    ));
                }
            }
        }
    };

    // Validate input file
    print_step(2, "Validating input file");
    validate_mrpack_file(&input_path)?;

    // Determine output directory
    let output_dir = match output {
        Some(path) => path.as_ref().to_path_buf(),
        None => std::env::current_dir()
            .with_context(|| "Failed to get current directory")?
            .join("extracted"),
    };

    print_step(3, &format!("Extracting to: {}", output_dir.display()));

    // Extract the .mrpack file
    let _extraction = MrpackExtraction::extract(&input_path, &output_dir)?;
    println!(
        "Successfully extracted {} files",
        _extraction.index.files.len()
    );

    // Handle git operations
    if !no_git {
        print_step(4, "Processing git repository");

        if GitManager::exists(&output_dir) {
            println!("Found existing git repository");
            let git = GitManager::open(&output_dir)?;
            git.add_all()?;
            git.commit_with_prompt()?;
        } else {
            println!("No git repository found. Initializing new repository...");
            let git = GitManager::init(&output_dir)?;
            git.add_all()?;
            git.commit_with_prompt()?;
        }
    } else {
        println!("Skipping git operations (--no-git flag specified)");
    }

    println!("\n✅ Extraction completed successfully!");
    Ok(())
}

/// Main application logic for generating server packs
pub fn generate_server_pack<P: AsRef<Path>>(
    input: Option<P>,
    list_file: P,
    output: Option<P>,
) -> Result<()> {
    let list_file = list_file.as_ref();

    print_header("Gitrinth - Server Pack Generation");

    // Load configuration
    print_step(1, "Loading configuration");
    let config = GitrinthConfig::load()?;

    // Determine input file (auto-detect if not provided)
    let input_path = match input {
        Some(path) => path.as_ref().to_path_buf(),
        None => {
            print_step(2, "Auto-detecting .mrpack file");
            match auto_detect_mrpack_file()? {
                Some(path) => path,
                None => {
                    return Err(anyhow::anyhow!(
                        "No .mrpack file specified and could not auto-detect one. Please specify with --input"
                    ));
                }
            }
        }
    };

    // Determine output file (generate default if not provided)
    let output_path = match output {
        Some(path) => path.as_ref().to_path_buf(),
        None => {
            let default_output =
                generate_server_pack_name(&input_path, config.get_server_pack_suffix());
            println!("🎯 Using default output name: {}", default_output.display());
            default_output
        }
    };

    // Validate input file
    print_step(3, "Validating input file");
    validate_mrpack_file(&input_path)?;

    // Create sample client mods list if it doesn't exist
    if !list_file.exists() {
        print_step(4, "Creating sample client-mods.txt file");
        create_sample_client_mods_list(list_file)?;
        println!("Please edit the client-mods.txt file and run the command again.");
        return Ok(());
    }

    // Load client mods list
    print_step(4, "Loading client mods list");
    let client_mods = ClientModsList::from_file(list_file)?;
    println!("Loaded {} client mod patterns", client_mods.mod_names.len());

    // Create temporary directory for processing
    print_step(5, "Extracting modpack for processing");
    let temp_dir = crate::utils::create_temp_dir("gitrinth-server-")?;
    let mut extraction = MrpackExtraction::extract(input_path, temp_dir.path().to_path_buf())?;
    println!("Extracted {} files", extraction.index.files.len());

    // Apply filters
    print_step(6, "Filtering client-side mods and excluded directories");
    let mut filter = ServerPackFilter::new(&mut extraction, &client_mods);
    let results = filter.apply_filters(&config)?;
    results.print_summary();

    // Create server pack
    print_step(
        7,
        &format!("Creating server pack: {}", output_path.display()),
    );
    extraction.create_mrpack(&output_path)?;

    println!("\n✅ Server pack generated successfully!");
    println!("Output: {}", output_path.display());
    Ok(())
}

/// Unpack .mrpack file by downloading mods and combining with overrides
pub fn unpack_mrpack<P: AsRef<Path>>(
    input: Option<P>,
    output: Option<P>,
    force: bool,
) -> Result<()> {
    unpack_mrpack_with_mode(input, output, force, None)
}

/// Unpack .mrpack file with a specific replacement mode (for testing)
pub fn unpack_mrpack_with_mode<P: AsRef<Path>>(
    input: Option<P>,
    output: Option<P>,
    force: bool,
    replacement_mode_override: Option<ReplacementMode>,
) -> Result<()> {
    print_header("Gitrinth - Modpack Unpacking");

    // Load configuration
    print_step(1, "Loading configuration");
    let config = GitrinthConfig::load()?;

    // Determine input file (auto-detect if not provided)
    let input_path = match input {
        Some(path) => path.as_ref().to_path_buf(),
        None => {
            print_step(2, "Auto-detecting .mrpack file");
            match auto_detect_mrpack_file()? {
                Some(path) => path,
                None => {
                    return Err(anyhow::anyhow!(
                        "No .mrpack file specified and could not auto-detect one. Please specify with --input"
                    ));
                }
            }
        }
    };

    // Determine output directory
    let output_path = match output {
        Some(path) => path.as_ref().to_path_buf(),
        None => {
            let default_output = PathBuf::from(&config.default_paths.unpack_dir);
            println!(
                "🎯 Using default unpack directory: {}",
                default_output.display()
            );
            default_output
        }
    };

    // Validate input file
    print_step(3, "Validating input file");
    validate_mrpack_file(&input_path)?;

    // Handle existing output directory and determine replacement mode
    print_step(4, "Checking output directory");
    let replacement_mode = if let Some(mode) = replacement_mode_override {
        // Use the provided mode for testing
        if output_path.exists() && !force {
            match mode {
                ReplacementMode::ReplaceAll => {
                    std::fs::remove_dir_all(&output_path)?;
                    std::fs::create_dir_all(&output_path)?;
                }
                ReplacementMode::ReplaceConflicting => {
                    // Directory exists, we'll handle selective replacement
                    println!("📝 Will replace only conflicting files and folders...");
                }
            }
        } else {
            std::fs::create_dir_all(&output_path)?;
        }
        mode
    } else if output_path.exists() {
        if !force {
            use dialoguer::{Confirm, Select};

            println!("⚠️  Directory already exists: {}", output_path.display());

            let options = vec![
                "Cancel operation",
                "Delete everything in directory",
                "Only replace conflicting files/folders",
            ];

            let selection = Select::new()
                .with_prompt("How would you like to proceed?")
                .items(&options)
                .default(0)
                .interact()?;

            match selection {
                0 => {
                    println!("❌ Operation cancelled by user.");
                    return Ok(());
                }
                1 => {
                    let confirmed = Confirm::new()
                        .with_prompt("Are you sure you want to delete everything in the directory?")
                        .default(false)
                        .interact()?;

                    if !confirmed {
                        println!("❌ Operation cancelled by user.");
                        return Ok(());
                    }

                    std::fs::remove_dir_all(&output_path)?;
                    std::fs::create_dir_all(&output_path)?;
                    ReplacementMode::ReplaceAll
                }
                2 => {
                    // We'll handle selective replacement during the unpacking process
                    println!("📝 Will replace only conflicting files and folders...");
                    ReplacementMode::ReplaceConflicting
                }
                _ => unreachable!(),
            }
        } else {
            // Force mode: delete everything
            std::fs::remove_dir_all(&output_path)?;
            std::fs::create_dir_all(&output_path)?;
            ReplacementMode::ReplaceAll
        }
    } else {
        std::fs::create_dir_all(&output_path)?;
        ReplacementMode::ReplaceAll
    };

    // Extract and process the modpack
    print_step(5, "Extracting modpack");
    let temp_dir = crate::utils::create_temp_dir("gitrinth-unpack-")?;
    let extraction = MrpackExtraction::extract(&input_path, &temp_dir.path().to_path_buf())?;
    println!(
        "Extracted {} files from index",
        extraction.index.files.len()
    );

    // Download mods to output directory
    print_step(6, "Downloading mods");

    // Download each mod file directly to output directory using the full path from index
    for (index, file) in extraction.index.files.iter().enumerate() {
        println!(
            "Downloading mod {}/{}: {}",
            index + 1,
            extraction.index.files.len(),
            file.path
        );

        // Use the full path from the index (e.g., "mods/sodium.jar")
        let mod_path = output_path.join(&file.path);
        if let Some(parent) = mod_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        // Download the file
        let response = reqwest::blocking::get(&file.downloads[0])?;
        let bytes = response.bytes()?;
        std::fs::write(&mod_path, bytes)?;
    }

    // Copy overrides
    if let Some(overrides_path) = &extraction.overrides_path {
        print_step(7, "Copying overrides");
        copy_dir_contents_with_mode(overrides_path, &output_path, replacement_mode)?;
    }

    println!(
        "✅ Modpack unpacked successfully to: {}",
        output_path.display()
    );
    println!("📁 {} mods downloaded", extraction.index.files.len());

    Ok(())
}

/// Full processing: extract and generate server pack
pub fn full_process<P: AsRef<Path>>(
    input: Option<P>,
    extract_dir: Option<P>,
    list_file: P,
    output: Option<P>,
    no_git: bool,
) -> Result<()> {
    let list_file = list_file.as_ref();

    print_header("Gitrinth - Full Processing Mode");

    // Step 1: Extract the modpack
    println!("🔄 Phase 1: Extracting modpack");
    let extract_dir_ref = extract_dir.as_ref().map(|p| p.as_ref());
    extract_mrpack(input.as_ref().map(|p| p.as_ref()), extract_dir_ref, no_git)?;

    // Step 2: Generate server pack
    println!("\n🔄 Phase 2: Generating server pack");
    generate_server_pack(
        input.as_ref().map(|p| p.as_ref()),
        list_file,
        output.as_ref().map(|p| p.as_ref()),
    )?;

    println!("\n✅ Full processing completed successfully!");
    Ok(())
}
