use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;

/// Configuration for Gitrinth application
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct GitrinthConfig {
    /// Configuration version for handling breaking changes
    pub version: u32,

    /// Suffix to append to server pack names (default: " (Server Pack)")
    pub server_pack_suffix: String,

    /// Files and directories to exclude from overrides folder when creating server packs
    pub excluded_overrides: Vec<String>,

    /// Default paths for various operations
    pub default_paths: DefaultPaths,
}

/// Default directory paths for various operations
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct DefaultPaths {
    /// Default directory for extract operations
    pub extract_dir: String,

    /// Default directory for unpack operations
    pub unpack_dir: String,
}

impl Default for GitrinthConfig {
    fn default() -> Self {
        Self {
            version: 1,
            server_pack_suffix: " (Server Pack)".to_string(),
            excluded_overrides: vec![
                "resourcepacks".to_string(),
                "shaderpacks".to_string(),
                "fancymenu_data".to_string(),
                "saves".to_string(),
                "figura".to_string(),
                "servers.dat".to_string(),
                "screenshots".to_string(),
                "logs".to_string(),
                "o_prof".to_string(),
                "configureddefaults".to_string(),
                ".qmenu_opened.marker".to_string(),
            ],
            default_paths: DefaultPaths::default(),
        }
    }
}

impl Default for DefaultPaths {
    fn default() -> Self {
        Self {
            extract_dir: "./extracted".to_string(),
            unpack_dir: "./unpacked".to_string(),
        }
    }
}

impl GitrinthConfig {
    /// Get the path to the configuration file
    pub fn config_path() -> Result<PathBuf> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| anyhow::anyhow!("Could not determine config directory"))?;

        let gitrinth_config_dir = config_dir.join("gitrinth");

        // Ensure the config directory exists
        if !gitrinth_config_dir.exists() {
            fs::create_dir_all(&gitrinth_config_dir).with_context(|| {
                format!(
                    "Failed to create config directory: {}",
                    gitrinth_config_dir.display()
                )
            })?;
        }

        Ok(gitrinth_config_dir.join("config.toml"))
    }

    /// Load configuration from file, creating default if it doesn't exist
    pub fn load() -> Result<Self> {
        let config_path = Self::config_path()?;

        if config_path.exists() {
            let content = fs::read_to_string(&config_path).with_context(|| {
                format!("Failed to read config file: {}", config_path.display())
            })?;

            let config: GitrinthConfig = toml::from_str(&content).with_context(|| {
                format!("Failed to parse config file: {}", config_path.display())
            })?;

            // Check version compatibility
            if config.version != 1 {
                return Err(anyhow::anyhow!(
                    "Unsupported config version {}. Expected version 1. Please run 'gitrinth config reset' to reset the config to defaults, which is compatible with the version of Gitrinth you are using.",
                    config.version
                ));
            }

            Ok(config)
        } else {
            // Create default config file
            let default_config = Self::default();
            default_config.save()?;
            println!(
                "Created default configuration file at: {}",
                config_path.display()
            );
            Ok(default_config)
        }
    }

    /// Save configuration to file
    pub fn save(&self) -> Result<()> {
        let config_path = Self::config_path()?;

        let content =
            toml::to_string_pretty(self).with_context(|| "Failed to serialize configuration")?;

        fs::write(&config_path, content)
            .with_context(|| format!("Failed to write config file: {}", config_path.display()))?;

        Ok(())
    }

    /// Check if a file or directory should be excluded from overrides
    pub fn should_exclude_override(&self, name: &str) -> bool {
        self.excluded_overrides
            .iter()
            .any(|excluded| name.to_lowercase() == excluded.to_lowercase())
    }

    /// Get the server pack suffix
    pub fn get_server_pack_suffix(&self) -> &str {
        &self.server_pack_suffix
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = GitrinthConfig::default();
        assert_eq!(config.version, 1);
        assert_eq!(config.server_pack_suffix, " (Server Pack)");
        assert!(config
            .excluded_overrides
            .contains(&"resourcepacks".to_string()));
        assert!(config
            .excluded_overrides
            .contains(&"shaderpacks".to_string()));
        assert!(config.excluded_overrides.contains(&"saves".to_string()));
        assert!(config.excluded_overrides.contains(&"figura".to_string()));
        assert!(config
            .excluded_overrides
            .contains(&"fancymenu_data".to_string()));
        assert_eq!(config.default_paths.extract_dir, "./extracted");
        assert_eq!(config.default_paths.unpack_dir, "./unpacked");
    }

    #[test]
    fn test_should_exclude_override() {
        let config = GitrinthConfig::default();

        // Test exact matches
        assert!(config.should_exclude_override("resourcepacks"));
        assert!(config.should_exclude_override("shaderpacks"));
        assert!(config.should_exclude_override("saves"));

        // Test case insensitive matching
        assert!(config.should_exclude_override("ResourcePacks"));
        assert!(config.should_exclude_override("SAVES"));

        // Test non-excluded items
        assert!(!config.should_exclude_override("mods"));
        assert!(!config.should_exclude_override("config"));
    }

    #[test]
    fn test_serialization() {
        let config = GitrinthConfig::default();
        let toml_str = toml::to_string(&config).unwrap();
        let deserialized: GitrinthConfig = toml::from_str(&toml_str).unwrap();

        assert_eq!(config.version, deserialized.version);
        assert_eq!(config.server_pack_suffix, deserialized.server_pack_suffix);
        assert_eq!(config.excluded_overrides, deserialized.excluded_overrides);
        assert_eq!(
            config.default_paths.extract_dir,
            deserialized.default_paths.extract_dir
        );
        assert_eq!(
            config.default_paths.unpack_dir,
            deserialized.default_paths.unpack_dir
        );
    }
}
