use anyhow::Result;
use clap::{Parser, Subcommand};
use std::path::PathBuf;

use gitrinth::{extract_mrpack, full_process, generate_server_pack, unpack_mrpack, GitrinthConfig};

#[derive(Parser)]
#[command(name = "gitrinth")]
#[command(
    about = "A fast tool for managing .mrpack based packs through Git, as well as generating server packs from them"
)]
#[command(version)]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Extract .mrpack file and commit to git repository
    Extract {
        /// Path to the .mrpack file (optional, auto-detects if only one .mrpack in directory)
        #[arg(short, long)]
        input: Option<PathBuf>,

        /// Directory to extract to (optional, defaults to current directory)
        #[arg(short, long)]
        output: Option<PathBuf>,

        /// Skip git operations (useful when no git repo is available)
        #[arg(long)]
        no_git: bool,
    },
    /// Generate server pack from .mrpack file
    ServerPack {
        /// Path to the .mrpack file (optional, auto-detects if only one .mrpack in directory)
        #[arg(short, long)]
        input: Option<PathBuf>,

        /// Path to client mods list file
        #[arg(short, long, default_value = "client-mods.txt")]
        list: PathBuf,

        /// Output path for server pack (optional, defaults to input name with " (Server Pack)" suffix)
        #[arg(short, long)]
        output: Option<PathBuf>,
    },
    /// Extract and generate server pack in one command
    Full {
        /// Path to the .mrpack file (optional, auto-detects if only one .mrpack in directory)
        #[arg(short, long)]
        input: Option<PathBuf>,

        /// Directory to extract to (optional, defaults to current directory)
        #[arg(short = 'd', long)]
        extract_dir: Option<PathBuf>,

        /// Path to client mods list file
        #[arg(short, long, default_value = "client-mods.txt")]
        list: PathBuf,

        /// Output path for server pack (optional, defaults to input name with " (Server Pack)" suffix)
        #[arg(short, long)]
        output: Option<PathBuf>,

        /// Skip git operations (useful when no git repo is available)
        #[arg(long)]
        no_git: bool,
    },
    /// Unpack .mrpack file by downloading mods and combining with overrides
    Unpack {
        /// Path to the .mrpack file (optional, auto-detects if only one .mrpack in directory)
        #[arg(short, long)]
        input: Option<PathBuf>,

        /// Directory to unpack to (optional, uses config default)
        #[arg(short, long)]
        output: Option<PathBuf>,

        /// Force overwrite existing directory without prompting
        #[arg(long)]
        force: bool,
    },
    /// Manage Gitrinth configuration
    Config {
        #[command(subcommand)]
        action: ConfigAction,
    },
}

#[derive(Subcommand)]
enum ConfigAction {
    /// Edit the configuration file in your default editor
    Edit,
    /// Show the current configuration
    Show,
    /// Show the path to the configuration file
    Path,
    /// Reset configuration to defaults
    Reset,
}

fn main() -> Result<()> {
    let cli = Cli::parse();

    match cli.command {
        Commands::Extract {
            input,
            output,
            no_git,
        } => {
            extract_mrpack(input, output, no_git)?;
        }
        Commands::ServerPack {
            input,
            list,
            output,
        } => {
            generate_server_pack(input, list, output)?;
        }
        Commands::Full {
            input,
            extract_dir,
            list,
            output,
            no_git,
        } => {
            full_process(input, extract_dir, list, output, no_git)?;
        }
        Commands::Unpack {
            input,
            output,
            force,
        } => {
            unpack_mrpack(input, output, force)?;
        }
        Commands::Config { action } => {
            handle_config_command(action)?;
        }
    }

    Ok(())
}

/// Handle config subcommands
fn handle_config_command(action: ConfigAction) -> Result<()> {
    match action {
        ConfigAction::Edit => {
            edit_config()?;
        }
        ConfigAction::Show => {
            show_config()?;
        }
        ConfigAction::Path => {
            show_config_path()?;
        }
        ConfigAction::Reset => {
            reset_config()?;
        }
    }
    Ok(())
}

/// Edit the configuration file in the user's default editor
fn edit_config() -> Result<()> {
    // Ensure config exists and load it to validate
    let _config = GitrinthConfig::load()?;
    let config_path = GitrinthConfig::config_path()?;

    println!("Opening configuration file: {}", config_path.display());

    // Try to open with the system's default editor
    #[cfg(windows)]
    {
        // Try to use the system's default editor for .toml files first
        let result = std::process::Command::new("cmd")
            .args(&["/C", "start", "", &config_path.to_string_lossy()])
            .spawn();

        match result {
            Ok(mut child) => {
                child.wait()?;
            }
            Err(_) => {
                // Fallback to notepad if the default editor fails
                std::process::Command::new("notepad")
                    .arg(&config_path)
                    .spawn()
                    .map_err(|e| {
                        anyhow::anyhow!(
                            "Failed to open editor: {}. Please edit the file manually at: {}",
                            e,
                            config_path.display()
                        )
                    })?
                    .wait()?;
            }
        }
    }

    #[cfg(not(windows))]
    {
        let editor = std::env::var("EDITOR").unwrap_or_else(|_| "nano".to_string());
        std::process::Command::new(&editor)
            .arg(&config_path)
            .status()
            .map_err(|e| {
                anyhow::anyhow!(
                    "Failed to open editor '{}': {}. Please edit the file manually at: {}",
                    editor,
                    e,
                    config_path.display()
                )
            })?;
    }

    // Validate the config after editing
    match GitrinthConfig::load() {
        Ok(_) => {
            println!("✅ Configuration file is valid!");
        }
        Err(e) => {
            eprintln!("❌ Error in configuration file: {}", e);
            eprintln!(
                "Please fix the configuration file at: {}",
                config_path.display()
            );
            return Err(e);
        }
    }

    Ok(())
}

/// Show the current configuration
fn show_config() -> Result<()> {
    let config = GitrinthConfig::load()?;
    let config_path = GitrinthConfig::config_path()?;

    println!("Configuration file: {}", config_path.display());
    println!();

    let toml_content = toml::to_string_pretty(&config)
        .map_err(|e| anyhow::anyhow!("Failed to serialize configuration: {}", e))?;

    println!("{}", toml_content);

    Ok(())
}

/// Show the path to the configuration file
fn show_config_path() -> Result<()> {
    let config_path = GitrinthConfig::config_path()?;
    println!("{}", config_path.display());
    Ok(())
}

/// Reset configuration to defaults
fn reset_config() -> Result<()> {
    let config_path = GitrinthConfig::config_path()?;

    println!("Resetting configuration to defaults...");

    let default_config = GitrinthConfig::default();
    default_config.save()?;

    println!("✅ Configuration reset to defaults!");
    println!("Configuration file: {}", config_path.display());

    Ok(())
}
