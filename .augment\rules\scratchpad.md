---
type: "manual"
description: "Useful directions on good ways to use a file as a scratchpad"
---
As you code, you'll have access to a file called scratchpad.md, or, if it doesn't exist, create it in the root directory of the project. You can use it to store any information to look back at later as you code, which is useful because you are limited in how much you can think back by how many tokens ago it was. Some examples of useful info to store in this file are:

- Your thought process about a feature or task as you code
- Plans (project structure, phases of development, or any other form of planning)
- (Specific) implementation details
- More detailed info about your current and future tasks (note: you already have access to a built-in task list, which you should continue to use however it can't be as detailed as what you can store in this file)
- Why you designed something a certain way
- Anything that needs more work in the future and what needs to be done
- Anything else you might want to use later

Make sure to continuously update this file as you work, and refer back to it often.