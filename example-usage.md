# Gitrinth Usage Examples

This document provides practical examples of how to use Gitrinth for common scenarios.

## Scenario 1: First Time Setup

You have a modpack file and want to create a server pack:

```bash
# Step 1: Simply run with smart defaults (auto-detects .mrpack file)
gitrinth server-pack

# This will:
# - Auto-detect your .mrpack file if only one exists
# - Create client-mods.txt if it doesn't exist
# - Use default output name: "YourModpack (Server Pack).mrpack"

# Step 2: Edit the generated client-mods.txt file
# Add your client-side mods with inline comments:
# sodium # performance mod
# jei # recipe viewer
# journeymap # minimap

# Step 3: Run the command again to generate the actual server pack
gitrinth server-pack
```

## Scenario 2: Extract Modpack for Development

You want to extract a modpack and track changes with git:

```bash
# Extract to a specific directory and set up git tracking
gitrinth extract -i MyModpack-1.0.0.mrpack -o ./modpack-dev

# This will:
# 1. Extract all files to ./modpack-dev
# 2. Initialize a git repository (if none exists)
# 3. Add all files to git
# 4. Prompt you for a commit message
```

## Scenario 3: Full Workflow

You want to both extract for development AND create a server pack:

```bash
# Do everything in one command
gitrinth full -i MyModpack-1.0.0.mrpack -d ./modpack-dev -o MyModpack-Server-1.0.0.mrpack

# This will:
# 1. Extract modpack to ./modpack-dev with git tracking
# 2. Create/use client-mods.txt for filtering
# 3. Generate server pack at MyModpack-Server-1.0.0.mrpack
```

## Scenario 4: Working Without Git

If you don't want git integration:

```bash
# Extract without git operations
gitrinth extract -i MyModpack-1.0.0.mrpack -o ./modpack-files --no-git

# Or full processing without git
gitrinth full -i MyModpack-1.0.0.mrpack -d ./modpack-files -o server.mrpack --no-git
```

## Scenario 5: Custom Client Mods List

Using a custom list file:

```bash
# Use a specific client mods list file
gitrinth server-pack -i MyModpack-1.0.0.mrpack -l my-custom-list.txt -o server.mrpack
```

## Scenario 6: Understanding the Confirmation Process

When generating server packs, Gitrinth shows you exactly what will be removed:

```bash
gitrinth server-pack

# Output will show:
# 📋 Removal Preview:
# ==================
#
# 🗂️  Mods to remove from modrinth index (3):
#    - mods/sodium-fabric-mc1.19.2-0.4.4+build.18.jar
#    - mods/jei-1.19.2-11.5.0.297-fabric.jar
#    - mods/journeymap-1.19.2-5.9.7-fabric.jar
#
# 📁 Files to remove from overrides (1):
#    - OptiFine_1.19.2_HD_U_H9.jar
#
# Do you want to proceed with removing these mods? [Y/n]
```

This gives you full control and prevents accidental removal of important mods.

## Scenario 7: Batch Processing

Processing multiple modpacks:

```bash
# Windows PowerShell
Get-ChildItem *.mrpack | ForEach-Object {
    $name = $_.BaseName
    gitrinth server-pack -i $_.Name -o "$name-server.mrpack"
}

# Linux/macOS Bash
for file in *.mrpack; do
    name=$(basename "$file" .mrpack)
    gitrinth server-pack -i "$file" -o "${name}-server.mrpack"
done


## Tips and Best Practices

1. **Always backup your original .mrpack files** before processing
2. **Test your server pack** by installing it and checking that all server-side mods are present
3. **Keep your client-mods.txt updated** as you add or remove mods from your modpack
4. **Use descriptive output names** to avoid confusion between client and server packs
5. **Check the filtering summary** to ensure the right mods were removed

## Troubleshooting

### "File does not exist" error
- Make sure the .mrpack file path is correct
- Use quotes around paths with spaces: `"My Modpack.mrpack"`

### "Failed to parse modrinth.index.json" error
- The .mrpack file might be corrupted
- Try re-downloading the modpack

### Git-related errors
- Use `--no-git` flag if you don't need git integration
- Make sure you have git installed and configured

### No mods were filtered
- Check that your client-mods.txt contains the correct mod names
- Remember that matching is partial and case-insensitive
- Check the mod filenames in your modpack to ensure they match your patterns
