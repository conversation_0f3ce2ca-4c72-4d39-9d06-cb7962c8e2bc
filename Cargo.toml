[package]
name = "gitrinth"
version = "0.1.0"
edition = "2024"
description = "A fast tool for managing .mrpack based packs through Git (and generating server packs from them)"
authors = ["StormDragon_64"]
license = "MIT"

[dependencies]
clap = { version = "4.5.41", features = ["derive"] }
zip = "4.3.0"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.141"
git2 = "0.20.2"
walkdir = "2.5.0"
anyhow = "1.0.98"
dialoguer = "0.11.0"
tempfile = "3.20.0"
dirs = "6.0.0"
toml = "0.9.2"
reqwest = { version = "0.12.22", features = ["blocking"] }
