use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs::{self, File};
use std::io::{<PERSON>ufReader, BufWriter};
use std::path::{Path, PathBuf};
use zip::{ZipArchive, ZipWriter};

/// Represents the modrinth.index.json file structure
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct ModrinthIndex {
    #[serde(rename = "formatVersion")]
    pub format_version: u32,
    pub game: String,
    #[serde(rename = "versionId")]
    pub version_id: String,
    pub name: String,
    pub summary: Option<String>,
    pub files: Vec<ModFile>,
    pub dependencies: HashMap<String, String>,
}

/// Represents a mod file in the modrinth index
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct ModFile {
    pub path: String,
    pub hashes: HashMap<String, String>,
    pub env: Option<ModEnvironment>,
    pub downloads: Vec<String>,
    #[serde(rename = "fileSize")]
    pub file_size: u64,
}

/// Represents the environment requirements for a mod
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct ModEnvironment {
    pub client: String,
    pub server: String,
}

/// Represents an extracted .mrpack file
pub struct MrpackExtraction {
    pub index: ModrinthIndex,
    pub extraction_path: PathBuf,
    pub overrides_path: Option<PathBuf>,
}

impl MrpackExtraction {
    /// Extract a .mrpack file to the specified directory
    pub fn extract<P: AsRef<Path>>(mrpack_path: P, output_dir: P) -> Result<Self> {
        let mrpack_path = mrpack_path.as_ref();
        let output_dir = output_dir.as_ref();

        // Create output directory if it doesn't exist
        fs::create_dir_all(output_dir).with_context(|| {
            format!(
                "Failed to create output directory: {}",
                output_dir.display()
            )
        })?;

        // Open the .mrpack file (which is a ZIP archive)
        let file = File::open(mrpack_path)
            .with_context(|| format!("Failed to open .mrpack file: {}", mrpack_path.display()))?;
        let mut archive = ZipArchive::new(BufReader::new(file))
            .with_context(|| "Failed to read .mrpack file as ZIP archive")?;

        // Extract all files
        for i in 0..archive.len() {
            let mut file = archive
                .by_index(i)
                .with_context(|| format!("Failed to read file at index {}", i))?;

            let outpath = match file.enclosed_name() {
                Some(path) => output_dir.join(path),
                None => continue, // Skip files with invalid names
            };

            if file.name().ends_with('/') {
                // Directory
                fs::create_dir_all(&outpath).with_context(|| {
                    format!("Failed to create directory: {}", outpath.display())
                })?;
            } else {
                // File
                if let Some(parent) = outpath.parent() {
                    fs::create_dir_all(parent).with_context(|| {
                        format!("Failed to create parent directory: {}", parent.display())
                    })?;
                }

                let mut outfile = File::create(&outpath)
                    .with_context(|| format!("Failed to create file: {}", outpath.display()))?;
                std::io::copy(&mut file, &mut outfile)
                    .with_context(|| format!("Failed to extract file: {}", outpath.display()))?;
            }
        }

        // Read the modrinth.index.json file
        let index_path = output_dir.join("modrinth.index.json");
        let index_file = File::open(&index_path).with_context(|| {
            format!(
                "Failed to open modrinth.index.json at: {}",
                index_path.display()
            )
        })?;
        let index: ModrinthIndex = serde_json::from_reader(BufReader::new(index_file))
            .with_context(|| "Failed to parse modrinth.index.json")?;

        // Check if overrides directory exists
        let overrides_path = output_dir.join("overrides");
        let overrides_path = if overrides_path.exists() {
            Some(overrides_path)
        } else {
            None
        };

        Ok(MrpackExtraction {
            index,
            extraction_path: output_dir.to_path_buf(),
            overrides_path,
        })
    }

    /// Create a new .mrpack file from the current extraction
    pub fn create_mrpack<P: AsRef<Path>>(&self, output_path: P) -> Result<()> {
        let output_path = output_path.as_ref();

        // Create the output file
        let file = File::create(output_path)
            .with_context(|| format!("Failed to create output file: {}", output_path.display()))?;
        let mut zip = ZipWriter::new(BufWriter::new(file));

        // Add modrinth.index.json
        let options = zip::write::FileOptions::<()>::default()
            .compression_method(zip::CompressionMethod::Deflated);
        zip.start_file("modrinth.index.json", options)?;
        serde_json::to_writer_pretty(&mut zip, &self.index)
            .with_context(|| "Failed to write modrinth.index.json to archive")?;

        // Add overrides directory if it exists
        if let Some(overrides_path) = &self.overrides_path {
            self.add_directory_to_zip(&mut zip, overrides_path, "overrides")?;
        }

        zip.finish()
            .with_context(|| "Failed to finalize ZIP archive")?;

        Ok(())
    }

    /// Helper function to add a directory to a ZIP archive
    fn add_directory_to_zip<W: std::io::Write + std::io::Seek>(
        &self,
        zip: &mut ZipWriter<W>,
        dir_path: &Path,
        zip_prefix: &str,
    ) -> Result<()> {
        use walkdir::WalkDir;

        let options = zip::write::FileOptions::<()>::default()
            .compression_method(zip::CompressionMethod::Deflated);

        for entry in WalkDir::new(dir_path) {
            let entry = entry.with_context(|| {
                format!("Failed to read directory entry in: {}", dir_path.display())
            })?;
            let path = entry.path();

            if path.is_file() {
                let relative_path = path.strip_prefix(dir_path).with_context(|| {
                    format!("Failed to get relative path for: {}", path.display())
                })?;
                let zip_path =
                    format!("{}/{}", zip_prefix, relative_path.display()).replace('\\', "/");

                zip.start_file(&zip_path, options)?;
                let mut file = File::open(path)
                    .with_context(|| format!("Failed to open file: {}", path.display()))?;
                std::io::copy(&mut file, zip).with_context(|| {
                    format!("Failed to copy file to archive: {}", path.display())
                })?;
            }
        }

        Ok(())
    }
}
