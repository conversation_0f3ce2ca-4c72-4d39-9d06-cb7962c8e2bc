# Gitrinth

A Rust based CLI tool for managing .mrpack based packs through Git, as well as generating server packs from them.

![Gitrinth Logo](gitrinth_logo.png)

## Features

- **Fast Processing**: Built in Rust for maximum performance
- **Git Integration**: Automatically commit extracted modpacks to git repositories
- **Smart Filtering**: Remove client-side mods using partial name matching
- **Flexible Operation**: Extract-only, server-pack-only, or full processing modes
- **User-Friendly**: Interactive prompts and clear progress indicators

## Installation

### From Source

1. Make sure you have [Rust](https://rustup.rs/) installed
2. Clone this repository:
   ```bash
   git clone <repository-url>
   cd Gitrinth
   ```
3. Build the project:
   ```bash
   cargo build --release
   ```
4. The executable will be available at `target/release/gitrinth.exe` (Windows) or `target/release/gitrinth` (Linux/macOS)

## Usage

Gitrinth has three main modes of operation:

### 1. Extract Mode

Extract a .mrpack file and optionally commit to a git repository:

```bash
gitrinth extract --input modpack.mrpack --output ./extracted
```

**Smart Defaults:**
```bash
# Auto-detect .mrpack file and use default extraction directory
gitrinth extract
```

Options:
- `--input, -i`: Path to the .mrpack file (optional, auto-detects if only one .mrpack in directory)
- `--output, -o`: Directory to extract to (optional, defaults to `./extracted`)
- `--no-git`: Skip git operations

### 2. Server Pack Mode

Generate a server pack by filtering out client-side mods:

```bash
gitrinth server-pack --input modpack.mrpack --output server-pack.mrpack
```

**Smart Defaults:**
```bash
# Auto-detect .mrpack file and use default output name
gitrinth server-pack

# Specify input, auto-generate output name
gitrinth server-pack --input "My Modpack.mrpack"
```

Options:
- `--input, -i`: Path to the .mrpack file (optional, auto-detects if only one .mrpack in directory)
- `--output, -o`: Output path for server pack (optional, defaults to input name with " (Server Pack)" suffix)
- `--list, -l`: Path to client mods list file (optional, defaults to `client-mods.txt`)

### 3. Full Processing Mode

Extract modpack and generate server pack in one command:

```bash
gitrinth full --input modpack.mrpack --extract-dir ./extracted --output server-pack.mrpack
```

**Smart Defaults:**
```bash
# Auto-detect input, use default directories and output name
gitrinth full
```

Options:
- `--input, -i`: Path to the .mrpack file (optional, auto-detects if only one .mrpack in directory)
- `--extract-dir, -d`: Directory to extract to (optional, defaults to `./extracted`)
- `--output, -o`: Output path for server pack (optional, defaults to input name with " (Server Pack)" suffix)
- `--list, -l`: Path to client mods list file (optional, defaults to `client-mods.txt`)
- `--no-git`: Skip git operations

## Client Mods List Format

The client mods list file (`client-mods.txt` by default) uses a simple format:

```
# Client-side mods list for Gitrinth
# Add mod names (partial matching supported) one per line
# Lines starting with # are comments and will be ignored
# Empty lines are also ignored

# Performance mods (client-only)
sodium
immediatelyfast
particle # matches all mods with particle in the jar name


# Client-side utility mods
allow-portal-gui


# Client-side cosmetic mods
iris

```

### Key Features:
- **Partial Matching**: "Ryoamic" will match "RyoamicLights-forge-0.2.3+mc1.20.1.jar"
- **Case Insensitive**: Works regardless of case
- **Comments**: Lines starting with `#` are ignored
- **Inline Comments**: Text after `#` on the same line is ignored (e.g., `sodium # performance mod`)
- **Empty Lines**: Blank lines are ignored
- **User Confirmation**: Shows preview of mods to be removed and asks for confirmation when running the command

If the file doesn't exist, Gitrinth will create a sample file for you to edit.

## How It Works

1. **Extraction**: Gitrinth extracts .mrpack files (which are ZIP archives) and parses the `modrinth.index.json` file
2. **Filtering**: It removes mods from both the modrinth index and the overrides folder based on your client mods list
3. **Git Integration**: Automatically detects or initializes git repositories and commits changes
4. **Server Pack Creation**: Generates a new .mrpack file with client-side mods filtered out

## Examples

### Basic Server Pack Generation
```bash
# Auto-detect .mrpack file and use smart defaults
gitrinth server-pack

# Or specify input file, auto-generate output name
gitrinth server-pack -i my-modpack.mrpack

# Traditional explicit approach
gitrinth server-pack -i my-modpack.mrpack -o my-server-pack.mrpack
```

### Extract and Commit to Git
```bash
gitrinth extract -i my-modpack.mrpack -o ./modpack-files
# This will prompt for a commit message
```

### Full Processing
```bash
gitrinth full -i my-modpack.mrpack -d ./modpack-files -o my-server-pack.mrpack
```

### Unpack Command

The `unpack` command downloads all mods from a .mrpack file and combines them with the overrides folder to create a complete modpack directory:

```bash
# Unpack to default directory (./unpacked)
gitrinth unpack

# Unpack to specific directory
gitrinth unpack -o /path/to/my-modpack

# Force overwrite existing directory
gitrinth unpack --force

# Specify input file
gitrinth unpack -i mypack.mrpack -o ./my-unpacked-modpack
```

When unpacking to an existing directory, you'll be prompted to:
- Cancel the operation
- Delete everything in the directory
- Only replace conflicting files/folders (mods folder is completely replaced)

## Configuration Management

Gitrinth provides convenient commands to manage your configuration:

```bash
# Edit configuration in your default editor
gitrinth config edit

# Show current configuration
gitrinth config show

# Show path to configuration file
gitrinth config path

# Reset configuration to defaults
gitrinth config reset
```

## Configuration

Gitrinth uses a configuration file to customize its behavior. The configuration file is automatically created on first run and stored in:

- **Windows**: `%APPDATA%\gitrinth\config.toml`
- **Linux/macOS**: `~/.config/gitrinth/config.toml`

### Configuration Options

```toml
# Suffix to append to server pack names (default: " (Server Pack)")
server_pack_suffix = " (Server Pack)"

# Configuration version (for handling breaking changes)
version = 1

# Files and directories to exclude from overrides folder when creating server packs
excluded_overrides = [
    "resourcepacks",
    "shaderpacks",
    "saves",
    "figura",
    "fancymenu_data",
]

# Default paths for various operations
[default_paths]
extract_dir = "./extracted"
unpack_dir = "./unpacked"
```

### Customizing Server Pack Names

You can change the default server pack naming by modifying the `server_pack_suffix`:

```toml
server_pack_suffix = "-server"  # Results in "MyModpack-server.mrpack"
server_pack_suffix = " [Server]"  # Results in "MyModpack [Server].mrpack"
```

### Excluding Files and Directories

The `excluded_overrides` setting controls which files and directories are removed from the overrides folder when creating server packs. This is useful for removing client-only content like resource packs, shader packs, save files, and specific configuration files.

You can customize this list based on your modpack's needs:

```toml
excluded_overrides = [
    "resourcepacks",
    "shaderpacks",
    "saves",
    "screenshots",
    "logs",
    "options.txt",
    "servers.dat",
]
```

### Default Paths

The `default_paths` section allows you to customize where operations output by default:

```toml
[default_paths]
extract_dir = "./extracted"      # Default for extract operations
unpack_dir = "./unpacked"        # Default for unpack operations
```

You can use absolute paths to output to specific locations:

```toml
[default_paths]
extract_dir = "C:/ModpackDev/extracted"
unpack_dir = "D:/Games/Minecraft/Modpacks"
```

## Error Handling

Gitrinth provides clear error messages for common issues:
- Invalid .mrpack files
- Missing client mods list
- Git configuration problems
- File permission issues
- Configuration file parsing errors

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
